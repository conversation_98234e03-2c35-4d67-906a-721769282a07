/**
 * Module pour la gestion des avis
 */
import API from '../api.js';

const ReviewsManager = {
    /**
     * Initialise le gestionnaire d'avis
     */
    init() {
        // Éléments DOM pour le dashboard
        this.reviewsContainer = document.querySelector('.reviews-container');
        if (this.reviewsContainer) {
            this.initDashboardReviews();
        }

        // Éléments DOM pour la page de détails du livre
        this.bookDetailsPage = document.querySelector('.book-details');
        if (this.bookDetailsPage) {
            this.initBookDetailsPage();
        }
    },

    /**
     * Initialise les avis dans le dashboard
     */
    initDashboardReviews() {
        // Boutons pour modifier un avis
        const editButtons = document.querySelectorAll('.edit-review');
        editButtons.forEach(button => {
            button.addEventListener('click', () => {
                const reviewId = button.dataset.reviewId;
                const rating = button.dataset.rating;
                const comment = button.dataset.comment;
                this.showEditReviewModal(reviewId, rating, comment);
            });
        });

        // Boutons pour supprimer un avis
        const deleteButtons = document.querySelectorAll('.delete-review');
        deleteButtons.forEach(button => {
            button.addEventListener('click', () => {
                const reviewId = button.dataset.reviewId;
                this.confirmDeleteReview(reviewId);
            });
        });

        // Modal d'avis
        const modal = document.getElementById('reviewModal');
        if (modal) {
            // Fermer la modal
            const closeBtn = modal.querySelector('.close');
            closeBtn.addEventListener('click', () => {
                modal.classList.remove('show');
                setTimeout(() => {
                    modal.style.display = 'none';
                }, 300);
            });

            const cancelBtn = document.getElementById('cancelReviewBtn');
            if (cancelBtn) {
                cancelBtn.addEventListener('click', () => {
                    modal.classList.remove('show');
                    setTimeout(() => {
                        modal.style.display = 'none';
                    }, 300);
                });
            }

            // Formulaire d'avis
            const reviewForm = document.getElementById('reviewForm');
            if (reviewForm) {
                reviewForm.addEventListener('submit', this.handleReviewFormSubmit.bind(this));
            }
        }

        // Initialiser les filtres et la recherche
        this.initFilters();
    },

    /**
     * Initialise les filtres et la recherche pour les avis
     */
    initFilters() {
        const searchInput = document.getElementById('search-reviews');
        const searchBtn = document.getElementById('search-reviews-btn');
        const sortSelect = document.getElementById('sort-reviews');
        const ratingFilter = document.getElementById('filter-rating');
        const applyBtn = document.getElementById('apply-reviews-filters');

        if (searchBtn && applyBtn) {
            // Recherche au clic sur le bouton
            searchBtn.addEventListener('click', () => {
                this.applyFilters();
            });

            // Recherche en appuyant sur Entrée
            searchInput?.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.applyFilters();
                }
            });

            // Appliquer les filtres au clic sur le bouton
            applyBtn.addEventListener('click', () => {
                this.applyFilters();
            });

            // Appliquer les filtres au changement de tri ou de note minimale
            sortSelect?.addEventListener('change', () => {
                this.applyFilters();
            });

            ratingFilter?.addEventListener('change', () => {
                this.applyFilters();
            });
        }
    },

    /**
     * Applique les filtres et la recherche aux avis
     */
    async applyFilters() {
        const searchInput = document.getElementById('search-reviews');
        const sortSelect = document.getElementById('sort-reviews');
        const ratingFilter = document.getElementById('filter-rating');

        if (!searchInput || !sortSelect || !ratingFilter || !this.reviewsContainer) return;

        const search = searchInput.value.trim();
        const sort = sortSelect.value;
        const minRating = ratingFilter.value;

        try {
            // Afficher un indicateur de chargement
            this.reviewsContainer.innerHTML = `
                <div class="loading-spinner">
                    <i class="fas fa-spinner fa-spin"></i> Chargement...
                </div>
            `;

            // Récupérer les avis filtrés
            const response = await API.getUserReviews(search, sort, minRating);
            const { reviews } = response.data;

            // Mettre à jour l'affichage
            if (reviews && reviews.length > 0) {
                this.reviewsContainer.innerHTML = `
                    <div class="filter-result-message">${reviews.length} avis trouvé(s)</div>
                    <div class="reviews-list">
                        ${reviews.map(review => this.generateDashboardReviewHTML(review)).join('')}
                    </div>
                `;

                // Réinitialiser les événements
                this.initDashboardReviews();
            } else {
                this.reviewsContainer.innerHTML = `
                    <div class="empty-state">
                        <p>Aucun avis ne correspond à votre recherche.</p>
                        <button class="btn btn-secondary reset-filters" id="reset-reviews-filters">Réinitialiser les filtres</button>
                    </div>
                `;

                // Ajouter un événement pour réinitialiser les filtres
                const resetBtn = document.getElementById('reset-reviews-filters');
                if (resetBtn) {
                    resetBtn.addEventListener('click', () => {
                        searchInput.value = '';
                        sortSelect.value = 'date_desc';
                        ratingFilter.value = '0';
                        this.applyFilters();
                    });
                }
            }
        } catch (error) {
            console.error('Erreur lors de l\'application des filtres:', error);
            this.reviewsContainer.innerHTML = `
                <div class="error-message">
                    <p>Une erreur est survenue lors du chargement des avis.</p>
                    <button class="btn btn-secondary" id="retry-reviews">Réessayer</button>
                </div>
            `;

            // Ajouter un événement pour réessayer
            const retryBtn = document.getElementById('retry-reviews');
            if (retryBtn) {
                retryBtn.addEventListener('click', () => {
                    this.applyFilters();
                });
            }
        }
    },

    /**
     * Génère le HTML pour un avis dans le dashboard
     * @param {Object} review - Objet avis
     * @returns {string} - HTML de l'avis
     */
    generateDashboardReviewHTML(review) {
        return `
            <div class="review-card">
                <div class="review-book">
                    <a href="/books/${review.book.id}" class="book-link">
                        <div class="book-cover-small">
                            <img src="${review.book.cover_image_url || 'https://via.placeholder.com/300x450?text=Nookli'}" alt="Couverture de ${review.book.title}">
                        </div>
                        <div class="book-info-small">
                            <h4>${review.book.title}</h4>
                            <p class="book-author">${review.book.author}</p>
                        </div>
                    </a>
                </div>
                <div class="review-content">
                    <div class="review-rating">
                        ${this.generateStarsHTML(review.rating)}
                        <span class="review-date" data-date="${review.created_at}">
                            ${new Date(review.created_at).toLocaleDateString()}
                            ${new Date(review.updated_at) > new Date(review.created_at) ? '(modifié)' : ''}
                        </span>
                    </div>
                    ${review.comment ? `<p class="review-text">${review.comment}</p>` : '<p class="review-text text-muted">Pas de commentaire</p>'}
                    <div class="review-actions">
                        <button class="btn btn-text edit-review" data-review-id="${review.id}" data-book-id="${review.book.id}" data-rating="${review.rating}" data-comment="${review.comment || ''}">
                            <i class="fas fa-edit"></i> Modifier
                        </button>
                        <button class="btn btn-text delete-review" data-review-id="${review.id}">
                            <i class="fas fa-trash"></i> Supprimer
                        </button>
                    </div>
                </div>
            </div>
        `;
    },

    /**
     * Initialise les avis sur la page de détails du livre
     */
    initBookDetailsPage() {
        const bookId = this.getBookIdFromUrl();
        if (!bookId) return;

        // Charger les avis pour ce livre (public, pas besoin d'authentification)
        this.loadBookReviews(bookId);

        // Bouton pour donner un avis
        const reviewBtn = document.getElementById('reviewBtn');
        console.log('DEBUG: reviewBtn trouvé:', reviewBtn);
        if (reviewBtn) {
            reviewBtn.addEventListener('click', () => {
                console.log('DEBUG: Clic sur le bouton d\'avis détecté');
                // Vérifier si l'utilisateur est connecté avant d'appeler l'API
                if (this.isUserLoggedIn()) {
                    console.log('DEBUG: Utilisateur connecté, ouverture de la modal');
                    this.showAddReviewModal(bookId);
                } else {
                    console.warn('Utilisateur non connecté - redirection vers login');
                }
            });
        } else {
            console.error('DEBUG: Bouton reviewBtn non trouvé !');
        }

        // Bouton pour supprimer un avis
        const deleteReviewBtn = document.getElementById('deleteReviewBtn');
        if (deleteReviewBtn) {
            deleteReviewBtn.addEventListener('click', () => {
                // Récupérer l'ID de l'avis existant
                const existingReview = window.existingReview;
                if (existingReview && existingReview.id) {
                    this.confirmDeleteReview(existingReview.id);
                } else {
                    alert('Erreur: Impossible de trouver l\'avis à supprimer');
                }
            });
        }

        // Modal d'avis
        const modal = document.getElementById('reviewModal');
        if (modal) {
            // Fermer la modal
            const closeBtn = modal.querySelector('.close');
            closeBtn.addEventListener('click', () => {
                modal.classList.remove('show');
                setTimeout(() => {
                    modal.style.display = 'none';
                }, 300);
            });

            const cancelBtn = document.getElementById('cancelReviewBtn');
            if (cancelBtn) {
                cancelBtn.addEventListener('click', () => {
                    modal.classList.remove('show');
                    setTimeout(() => {
                        modal.style.display = 'none';
                    }, 300);
                });
            }

            // Formulaire d'avis
            const reviewForm = document.getElementById('reviewForm');
            if (reviewForm) {
                reviewForm.addEventListener('submit', this.handleReviewFormSubmit.bind(this));
            }
        }
    },

    /**
     * Récupère l'ID du livre depuis l'URL
     * @returns {number|null} - ID du livre ou null
     */
    getBookIdFromUrl() {
        const path = window.location.pathname;
        const match = path.match(/\/books\/(\d+)/);
        return match ? parseInt(match[1]) : null;
    },

    /**
     * Vérifie si l'utilisateur est connecté
     * @returns {boolean} - true si l'utilisateur est connecté, false sinon
     */
    isUserLoggedIn() {
        // Vérifier la présence d'éléments qui indiquent que l'utilisateur est connecté
        return document.querySelector('#reviewBtn') !== null &&
               !document.querySelector('.book-actions-guest');
    },

    /**
     * Charge les avis pour un livre
     * @param {number} bookId - ID du livre
     * @param {Object} options - Options de filtrage et pagination
     */
    async loadBookReviews(bookId, options = {}) {
        const container = document.getElementById('reviews-container');
        const paginationContainer = document.getElementById('reviews-pagination');

        container.innerHTML = '<div class="loading-spinner"><i class="fas fa-spinner fa-spin"></i> Chargement des avis...</div>';

        try {
            const response = await API.getBookReviews(bookId, options);
            const { reviews, pagination } = response.data;

            if (reviews.length === 0) {
                let message = 'Aucun avis pour ce livre pour le moment.';

                // Si des filtres sont appliqués, adapter le message
                if (options.minRating || options.maxRating || options.hasComment) {
                    message = 'Aucun avis ne correspond à vos critères de filtrage.';
                } else {
                    message += ' Soyez le premier à donner votre avis !';
                }

                container.innerHTML = `<div class="empty-state"><p>${message}</p></div>`;
                paginationContainer.innerHTML = '';
                return;
            }

            // Générer les filtres si c'est la première page sans filtres
            let filtersHtml = '';
            if (!options.minRating && !options.maxRating && !options.hasComment && (!options.page || options.page === 1)) {
                filtersHtml = `
                    <div class="reviews-filters">
                        <div class="filter-group">
                            <label>Filtrer par note:</label>
                            <div class="rating-filter">
                                ${this.generateRatingFilterHTML()}
                            </div>
                        </div>
                        <div class="filter-group">
                            <label>Tri:</label>
                            <select id="review-sort">
                                <option value="created_at-DESC" ${options.orderBy === 'created_at' && options.order === 'DESC' ? 'selected' : ''}>Plus récents</option>
                                <option value="created_at-ASC" ${options.orderBy === 'created_at' && options.order === 'ASC' ? 'selected' : ''}>Plus anciens</option>
                                <option value="rating-DESC" ${options.orderBy === 'rating' && options.order === 'DESC' ? 'selected' : ''}>Note (décroissante)</option>
                                <option value="rating-ASC" ${options.orderBy === 'rating' && options.order === 'ASC' ? 'selected' : ''}>Note (croissante)</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label>
                                <input type="checkbox" id="has-comment-filter" ${options.hasComment ? 'checked' : ''}>
                                Avec commentaire
                            </label>
                        </div>
                        <button id="apply-filters" class="btn btn-primary btn-sm">Appliquer</button>
                        <button id="reset-filters" class="btn btn-secondary btn-sm">Réinitialiser</button>
                    </div>
                `;
            }

            // Générer le HTML pour les avis
            let html = filtersHtml + '<div class="reviews-list">';
            reviews.forEach(review => {
                html += this.generateReviewHTML(review);
            });
            html += '</div>';
            container.innerHTML = html;

            // Ajouter les événements aux filtres
            if (filtersHtml) {
                const applyFiltersBtn = document.getElementById('apply-filters');
                const resetFiltersBtn = document.getElementById('reset-filters');
                const sortSelect = document.getElementById('review-sort');
                const hasCommentCheckbox = document.getElementById('has-comment-filter');
                const ratingFilters = document.querySelectorAll('.rating-filter input');

                if (applyFiltersBtn) {
                    applyFiltersBtn.addEventListener('click', () => {
                        const [orderBy, order] = sortSelect.value.split('-');
                        const hasComment = hasCommentCheckbox.checked;

                        // Récupérer les notes sélectionnées
                        let minRating = null;
                        let maxRating = null;

                        ratingFilters.forEach(input => {
                            if (input.checked) {
                                const rating = parseInt(input.value);
                                if (minRating === null || rating < minRating) {
                                    minRating = rating;
                                }
                                if (maxRating === null || rating > maxRating) {
                                    maxRating = rating;
                                }
                            }
                        });

                        this.loadBookReviews(bookId, {
                            page: 1,
                            orderBy,
                            order,
                            minRating,
                            maxRating,
                            hasComment
                        });
                    });
                }

                if (resetFiltersBtn) {
                    resetFiltersBtn.addEventListener('click', () => {
                        this.loadBookReviews(bookId);
                    });
                }
            }

            // Générer la pagination
            if (pagination.totalPages > 1) {
                let paginationHtml = '<div class="pagination">';
                for (let i = 1; i <= pagination.totalPages; i++) {
                    paginationHtml += `<button class="page-btn ${i === pagination.page ? 'active' : ''}" data-page="${i}">${i}</button>`;
                }
                paginationHtml += '</div>';
                paginationContainer.innerHTML = paginationHtml;

                // Ajouter les événements aux boutons de pagination
                const pageButtons = paginationContainer.querySelectorAll('.page-btn');
                pageButtons.forEach(button => {
                    button.addEventListener('click', () => {
                        const page = parseInt(button.dataset.page);
                        this.loadBookReviews(bookId, {
                            ...options,
                            page
                        });
                    });
                });
            } else {
                paginationContainer.innerHTML = '';
            }
        } catch (error) {
            container.innerHTML = `<div class="error-message">Erreur: ${error.message}</div>`;
            paginationContainer.innerHTML = '';
        }
    },

    /**
     * Génère le HTML pour les filtres de note
     * @returns {string} - HTML des filtres de note
     */
    generateRatingFilterHTML() {
        let html = '';
        for (let i = 5; i >= 1; i--) {
            html += `
                <label class="rating-filter-item">
                    <input type="checkbox" value="${i}">
                    ${this.generateStarsHTML(i)}
                </label>
            `;
        }
        return html;
    },

    /**
     * Génère le HTML pour un avis
     * @param {Object} review - Objet avis
     * @returns {string} - HTML de l'avis
     */
    generateReviewHTML(review) {
        return `
            <div class="review-card">
                <div class="review-header">
                    <div class="review-user">
                        <span class="user-name">${review.user.username}</span>
                    </div>
                    <div class="review-rating">
                        ${this.generateStarsHTML(review.rating)}
                        <span class="review-date">
                            ${new Date(review.created_at).toLocaleDateString()}
                            ${new Date(review.updated_at) > new Date(review.created_at) ? '(modifié)' : ''}
                        </span>
                    </div>
                </div>
                <div class="review-content">
                    ${review.comment ? `<p class="review-text">${review.comment}</p>` : '<p class="review-text text-muted">Pas de commentaire</p>'}
                </div>
            </div>
        `;
    },

    /**
     * Génère le HTML pour les étoiles de notation
     * @param {number} rating - Note (1-5)
     * @returns {string} - HTML des étoiles
     */
    generateStarsHTML(rating) {
        let html = '';
        for (let i = 1; i <= 5; i++) {
            if (i <= rating) {
                html += '<span class="star filled">★</span>';
            } else {
                html += '<span class="star">☆</span>';
            }
        }
        return html;
    },

    /**
     * Affiche la modal pour ajouter ou modifier un avis
     * @param {number} bookId - ID du livre
     */
    showAddReviewModal(bookId) {
        console.log('DEBUG: showAddReviewModal appelée avec bookId:', bookId);
        const modal = document.getElementById('reviewModal');
        const modalTitle = document.getElementById('reviewModalTitle');
        const reviewForm = document.getElementById('reviewForm');
        const reviewIdInput = document.getElementById('reviewId');
        const commentInput = document.getElementById('comment');

        console.log('DEBUG: Éléments de la modal trouvés:', {
            modal: !!modal,
            modalTitle: !!modalTitle,
            reviewForm: !!reviewForm,
            reviewIdInput: !!reviewIdInput,
            commentInput: !!commentInput
        });

        if (!modal) {
            console.error('DEBUG: Modal reviewModal non trouvée !');
            return;
        }

        // Vérifier s'il y a un avis existant
        const existingReview = window.existingReview;
        console.log('DEBUG: Avis existant:', existingReview);

        if (existingReview) {
            // Mode modification
            modalTitle.textContent = 'Modifier mon avis';
            reviewIdInput.value = existingReview.id;
            commentInput.value = existingReview.comment || '';
            reviewForm.dataset.action = 'edit';
            reviewForm.dataset.bookId = bookId;

            // Pré-sélectionner la note existante
            const ratingInputs = reviewForm.querySelectorAll('input[name="rating"]');
            ratingInputs.forEach(input => {
                input.checked = (parseInt(input.value) === existingReview.rating);
            });
        } else {
            // Mode création
            modalTitle.textContent = 'Donner mon avis';
            reviewIdInput.value = '';
            commentInput.value = '';
            reviewForm.dataset.action = 'create';
            reviewForm.dataset.bookId = bookId;

            // Réinitialiser les étoiles
            const ratingInputs = reviewForm.querySelectorAll('input[name="rating"]');
            ratingInputs.forEach(input => {
                input.checked = false;
            });
        }

        modal.style.display = 'block';
        // Ajouter la classe 'show' pour l'animation CSS
        setTimeout(() => {
            modal.classList.add('show');
        }, 10);
        console.log('DEBUG: Modal affichée avec display:', modal.style.display);
    },

    /**
     * Affiche la modal pour modifier un avis
     * @param {number} reviewId - ID de l'avis
     * @param {number} rating - Note actuelle
     * @param {string} comment - Commentaire actuel
     */
    showEditReviewModal(reviewId, rating, comment) {
        const modal = document.getElementById('reviewModal');
        const modalTitle = document.getElementById('reviewModalTitle');
        const reviewForm = document.getElementById('reviewForm');
        const reviewIdInput = document.getElementById('reviewId');
        const commentInput = document.getElementById('comment');

        modalTitle.textContent = 'Modifier mon avis';
        reviewIdInput.value = reviewId;
        commentInput.value = comment;
        reviewForm.dataset.action = 'edit';

        // Sélectionner la note actuelle
        const ratingInput = reviewForm.querySelector(`input[name="rating"][value="${rating}"]`);
        if (ratingInput) {
            ratingInput.checked = true;
        }

        modal.style.display = 'block';
    },

    /**
     * Gère la soumission du formulaire d'avis
     * @param {Event} e - Événement de soumission
     */
    async handleReviewFormSubmit(e) {
        e.preventDefault();
        const form = e.currentTarget;
        const action = form.dataset.action;
        const reviewId = document.getElementById('reviewId').value;
        const bookId = form.dataset.bookId;

        // Récupérer la note sélectionnée
        const ratingInput = form.querySelector('input[name="rating"]:checked');
        if (!ratingInput) {
            alert('Veuillez sélectionner une note');
            return;
        }
        const rating = ratingInput.value;

        const comment = document.getElementById('comment').value;

        try {
            if (action === 'create') {
                await API.createReview(bookId, rating, comment);
                // Recharger les avis
                this.loadBookReviews(bookId);
            } else if (action === 'edit') {
                await API.updateReview(reviewId, rating, comment);
                // Recharger la page pour voir les changements
                window.location.reload();
            }

            // Fermer la modal
            const modal = document.getElementById('reviewModal');
            modal.classList.remove('show');
            setTimeout(() => {
                modal.style.display = 'none';
            }, 300);
        } catch (error) {
            alert(`Erreur: ${error.message}`);
        }
    },

    /**
     * Demande confirmation avant de supprimer un avis
     * @param {number} reviewId - ID de l'avis
     */
    confirmDeleteReview(reviewId) {
        if (confirm('Êtes-vous sûr de vouloir supprimer cet avis ? Cette action est irréversible.')) {
            this.deleteReview(reviewId);
        }
    },

    /**
     * Supprime un avis
     * @param {number} reviewId - ID de l'avis
     */
    async deleteReview(reviewId) {
        try {
            await API.deleteReview(reviewId);
            // Recharger la page pour voir les changements
            window.location.reload();
        } catch (error) {
            alert(`Erreur: ${error.message}`);
        }
    }
};

export default ReviewsManager;
