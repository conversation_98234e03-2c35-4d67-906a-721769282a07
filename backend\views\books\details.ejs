<%- include('../partials/header') %>

<div class="container">
    <!-- Messages Flash -->
    <%- include('../partials/flash-messages') %>

    <div class="book-details">
        <div class="book-details-header">
            <div class="book-cover-large">
                <img src="<%= book.cover_url || book.cover_image_url || '/images/placeholder-cover.svg' %>" alt="Couverture de <%= book.title %>">
            </div>

            <div class="book-info-large">
                <h1><%= book.title %></h1>
                <p class="book-author-large">par <strong><%= book.author %></strong></p>

                <!-- Affichage du rating -->
                <% if (displayRating && displayRating > 0) { %>
                    <div class="book-rating">
                        <div class="rating-stars">
                            <% for (let i = 1; i <= 5; i++) { %>
                                <i class="<%= i <= Math.floor(displayRating) ? 'fas' : (i <= displayRating ? 'fas fa-star-half-alt' : 'far') %> fa-star"></i>
                            <% } %>
                        </div>
                        <span class="rating-value">(<%= displayRating %>/5)</span>
                        <% if (reviewCount > 0) { %>
                            <span class="rating-count">- <%= reviewCount %> avis</span>
                        <% } else { %>
                            <span class="rating-count">- Note initiale</span>
                        <% } %>
                    </div>
                <% } %>

                <div class="book-meta-large">
                    <% if (book.genre) { %>
                        <span class="book-genre-badge"><%= book.genre.name %></span>
                    <% } else { %>
                        <span class="book-genre-badge">Non catégorisé</span>
                    <% } %>

                    <% if (book.publication_year) { %>
                        <span class="book-year-badge"><%= book.publication_year %></span>
                    <% } %>

                    <% if (book.isbn) { %>
                        <span class="book-isbn">ISBN: <%= book.isbn %></span>
                    <% } %>
                </div>

                <div class="book-rating-large">
                    <% if (book.average_rating > 0) { %>
                        <div class="stars">
                            <% for (let i = 1; i <= 5; i++) { %>
                                <% if (i <= Math.round(book.average_rating)) { %>
                                    <span class="star filled">★</span>
                                <% } else { %>
                                    <span class="star">☆</span>
                                <% } %>
                            <% } %>
                        </div>
                        <span class="rating-value"><%= book.average_rating.toFixed(1) %>/5</span>
                    <% } else { %>
                        <span class="no-rating">Pas encore noté</span>
                    <% } %>
                </div>

                <!-- Actions pour les utilisateurs connectés -->
                <% if (currentUser) { %>
                    <div class="book-actions">
                        <button class="btn btn-primary" id="addToListBtn">
                            <i class="fas fa-plus"></i> Ajouter à ma liste
                        </button>
                        <button class="btn btn-secondary<%= isFavorite ? ' active' : '' %>" id="favoriteBtn">
                            <i class="<%= isFavorite ? 'fas' : 'far' %> fa-heart"></i> <%= isFavorite ? 'Retirer des favoris' : 'Ajouter aux favoris' %>
                        </button>
                        <button class="btn btn-secondary" id="reviewBtn">
                            <i class="<%= hasUserReviewed ? 'fas' : 'far' %> fa-star"></i> <%= hasUserReviewed ? 'Modifier mon avis' : 'Donner mon avis' %>
                        </button>
                    </div>
                <% } else { %>
                    <div class="book-actions-guest">
                        <p>Connectez-vous pour ajouter ce livre à vos listes, le marquer comme favori ou donner votre avis.</p>
                        <div class="guest-buttons">
                            <a href="/login?returnTo=<%= encodeURIComponent('/books/' + book.id) %>" class="btn btn-primary">Se connecter</a>
                            <a href="/register?returnTo=<%= encodeURIComponent('/books/' + book.id) %>" class="btn btn-secondary">S'inscrire</a>
                        </div>
                    </div>
                <% } %>
            </div>
        </div>

        <div class="book-description">
            <h2>Description</h2>
            <% if (book.description) { %>
                <p><%= book.description %></p>
            <% } else { %>
                <p class="no-description">Aucune description disponible pour ce livre.</p>
            <% } %>
        </div>

        <!-- Section des avis -->
        <div class="book-reviews">
            <h2>Avis des lecteurs</h2>
            <div id="reviews-container">
                <!-- Les avis seront chargés dynamiquement ici -->
                <div class="loading-spinner">
                    <i class="fas fa-spinner fa-spin"></i> Chargement des avis...
                </div>
            </div>
            <div class="reviews-pagination" id="reviews-pagination">
                <!-- La pagination sera générée dynamiquement ici -->
            </div>
        </div>

        <!-- Modal pour ajouter/modifier un avis -->
        <div class="modal" id="reviewModal">
            <div class="modal-content">
                <span class="close">&times;</span>
                <h3 id="reviewModalTitle">Donner mon avis</h3>
                <form id="reviewForm">
                    <input type="hidden" id="reviewId" value="">
                    <div class="form-group">
                        <label>Note</label>
                        <div class="rating-input">
                            <% for (let i = 5; i >= 1; i--) { %>
                                <input type="radio" id="star<%= i %>" name="rating" value="<%= i %>">
                                <label for="star<%= i %>">☆</label>
                            <% } %>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="comment">Commentaire (optionnel)</label>
                        <textarea id="comment" name="comment" rows="4"></textarea>
                    </div>
                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">Enregistrer</button>
                        <button type="button" class="btn btn-secondary" id="cancelReviewBtn">Annuler</button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Modal pour ajouter à une liste -->
        <div class="modal" id="listModal">
            <div class="modal-content">
                <span class="close">&times;</span>
                <h3>Ajouter à une liste</h3>
                <div id="reading-lists-container">
                    <!-- Les listes seront chargées dynamiquement ici -->
                    <div class="loading-spinner">
                        <i class="fas fa-spinner fa-spin"></i> Chargement des listes...
                    </div>
                </div>
                <div class="form-actions">
                    <button type="button" class="btn btn-secondary" id="cancelListBtn">Fermer</button>
                </div>
            </div>
        </div>
    </div>

    <div class="back-link">
        <a href="/books" class="btn btn-text">
            <i class="fas fa-arrow-left"></i> Retour au catalogue
        </a>
    </div>
</div>

<!-- Script pour passer les données de l'avis existant au JavaScript -->
<% if (currentUser && hasUserReviewed && userReview) { %>
<script>
    window.existingReview = {
        id: <%= userReview.id %>,
        rating: <%= userReview.rating %>,
        comment: '<%= userReview.comment ? userReview.comment.replace(/'/g, "\\'").replace(/\n/g, "\\n") : "" %>'
    };
</script>
<% } %>

<%- include('../partials/footer') %>
